#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试路径配置是否正确
"""
import os
import sys
sys.path.append('.')
import Config

def test_paths():
    print("=== 测试路径配置 ===")
    
    # 测试模型路径
    print("\n1. 测试模型路径:")
    for i, model_path in enumerate(Config.model_paths):
        exists = os.path.exists(model_path)
        status = "✅ 存在" if exists else "❌ 不存在"
        print(f"   {i+1}. {model_path} - {status}")
        if exists:
            size = os.path.getsize(model_path) / (1024*1024)  # MB
            print(f"      文件大小: {size:.1f} MB")
    
    # 测试背景图片路径
    print("\n2. 测试背景图片路径:")
    for i, bg_path in enumerate(Config.background_image_paths):
        exists = os.path.exists(bg_path)
        status = "✅ 存在" if exists else "❌ 不存在"
        print(f"   {i+1}. {bg_path} - {status}")
    
    # 测试其他重要路径
    print("\n3. 测试其他重要路径:")
    important_paths = [
        'UIProgram/style.css',
        'Font/platech.ttf',
        'test-file/images',
        'save_data'
    ]
    
    for path in important_paths:
        exists = os.path.exists(path)
        status = "✅ 存在" if exists else "❌ 不存在"
        print(f"   {path} - {status}")
    
    # 查找第一个可用的模型
    print("\n4. 查找第一个可用的模型:")
    for model_path in Config.model_paths:
        if os.path.exists(model_path):
            print(f"   找到可用模型: {model_path}")
            break
    else:
        print("   ❌ 未找到任何可用模型")
    
    # 查找第一个可用的背景图片
    print("\n5. 查找第一个可用的背景图片:")
    for bg_path in Config.background_image_paths:
        if os.path.exists(bg_path):
            print(f"   找到可用背景图片: {bg_path}")
            break
    else:
        print("   ❌ 未找到任何可用背景图片")

if __name__ == "__main__":
    test_paths()
