
QGroupBox {
    border: 2px solid #27ae60;
    border-radius: 10px;
    margin-top: 15px;
    font-weight: bold;
    background-color: rgba(255, 255, 255, 240);
    padding: 5px;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 15px;
    padding: 0 8px 0 8px;
    color: #27ae60;
    font-size: 14px;
    font-weight: bold;
}

QPushButton {
    color: white;
    background-color: #2ecc71;
    border-radius: 8px;
    padding: 8px 15px;
    font-weight: bold;
    font-size: 13px;
    min-height: 30px;
    border: none;
}

QPushButton#SaveBtn {
    color: white;
    background-color: #27ae60;
    border-radius: 8px;
    padding: 8px 15px;
    font-weight: bold;
    min-width: 120px;
}

QPushButton#ExitBtn {
    color: white;
    background-color: #e74c3c;
    border-radius: 8px;
    padding: 8px 15px;
    font-weight: bold;
    min-width: 120px;
}

QPushButton:hover {
    background-color: #16a085;
}

QPushButton#ExitBtn:hover {
    background-color: #c0392b;
}

QPushButton:pressed {
    background-color: #1abc9c;
    border: none;
}

QTableWidget {
    alternate-background-color: #e8f8f5;
    gridline-color: #bdc3c7;
    border: 2px solid #27ae60;
    border-radius: 8px;
    selection-background-color: #1abc9c;
    selection-color: white;
    font-size: 13px;
    font-family: "Microsoft YaHei", "SimSun", sans-serif;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #ecf0f1;
}

QTableWidget::item:selected {
    background-color: #1abc9c;
    color: white;
}

QHeaderView::section {
    background-color: #16a085;
    color: white;
    padding: 10px;
    border: none;
    font-weight: bold;
    font-size: 14px;
}

QLineEdit {
    border: 2px solid #27ae60;
    border-radius: 8px;
    padding: 8px;
    background-color: #f5f5f5;
    selection-background-color: #1abc9c;
    selection-color: white;
    min-height: 28px;
    font-size: 13px;
    font-family: "Microsoft YaHei", "SimSun", sans-serif;
}

QLineEdit:focus {
    border: 2px solid #16a085;
    background-color: white;
}

QLineEdit:disabled {
    background-color: #ecf0f1;
    color: #7f8c8d;
}

QComboBox {
    border: 2px solid #27ae60;
    border-radius: 8px;
    padding: 8px;
    background-color: #f5f5f5;
    min-height: 28px;
    selection-background-color: #1abc9c;
    selection-color: white;
    font-size: 13px;
    font-family: "Microsoft YaHei", "SimSun", sans-serif;
}

QComboBox:hover {
    border: 2px solid #16a085;
    background-color: #e8f8f5;
}

QComboBox:disabled {
    background-color: #ecf0f1;
    color: #7f8c8d;
}

QComboBox::drop-down {
    subcontrol-origin: padding;
    subcontrol-position: top right;
    width: 25px;
    border-left-width: 1px;
    border-left-color: #27ae60;
    border-left-style: solid;
    border-top-right-radius: 7px;
    border-bottom-right-radius: 7px;
}

QComboBox::down-arrow {
    width: 14px;
    height: 14px;
}

QProgressBar {
    border: 2px solid #27ae60;
    border-radius: 8px;
    text-align: center;
    background-color: #ecf0f1;
    padding: 2px;
    height: 18px;
    font-size: 12px;
    font-weight: bold;
    color: #2c3e50;
}

QProgressBar::chunk {
    background-color: #1abc9c;
    border-radius: 6px;
}

/* 垂直进度条 */
QProgressBar:vertical {
    width: 25px;
    min-height: 120px;
}

QLabel {
    color: #2c3e50;
    font-size: 13px;
    font-family: "Microsoft YaHei", "SimSun", sans-serif;
}

QLabel[objectName^="label_"] {
    font-weight: bold;
}

/* 标题标签 */
QLabel#label_3 {
    color: #27ae60;
    font-size: 22px;
    font-weight: bold;
    padding: 10px;
    font-family: "Microsoft YaHei", "SimHei", sans-serif;
    letter-spacing: 1px;
}

/* 数值标签 */
QLabel#label_nums, QLabel#label_conf, QLabel#time_lb,
QLabel#label_xmin, QLabel#label_ymin, QLabel#label_xmax, QLabel#label_ymax,
QLabel#type_lb {
    color: #16a085;
    font-size: 15px;
    font-weight: bold;
    font-family: "Arial", sans-serif;
}

/* 分类标签 */
QLabel#label_5, QLabel#label_10, QLabel#label_11, QLabel#label_13 {
    font-size: 14px;
    color: #34495e;
}

QTextEdit {
    border: 2px solid #27ae60;
    border-radius: 10px;
    padding: 12px;
    background-color: #f0f9f4;
    selection-background-color: #1abc9c;
    selection-color: white;
    font-family: "Microsoft YaHei", "SimSun", sans-serif;
    font-size: 14px;
    line-height: 1.6;
}

QTextEdit:focus {
    border: 2px solid #16a085;
    background-color: #ffffff;
}

#MainWindow {
    background-color: #f5f5f5;
}

/* 美化滚动条 */
QScrollBar:vertical {
    border: none;
    background: #f0f0f0;
    width: 10px;
    margin: 0px 0px 0px 0px;
    border-radius: 5px;
}

QScrollBar::handle:vertical {
    background: #27ae60;
    min-height: 30px;
    border-radius: 5px;
}

QScrollBar::handle:vertical:hover {
    background: #16a085;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    border: none;
    background: #f0f0f0;
    height: 10px;
    margin: 0px 0px 0px 0px;
    border-radius: 5px;
}

QScrollBar::handle:horizontal {
    background: #27ae60;
    min-width: 30px;
    border-radius: 5px;
}

QScrollBar::handle:horizontal:hover {
    background: #16a085;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}